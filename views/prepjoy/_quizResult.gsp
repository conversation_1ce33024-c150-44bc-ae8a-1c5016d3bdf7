<!-------- RESULT UI --------->
<style>
    .practice-result{
        height: 100vh;
    }
    .quiz-result-container {
        padding: 20px;
        background-color: #f8f9fa;
        min-height: 100vh;
    }

    .overall-summary-section {
        margin-bottom: 30px;
    }

    .overall-summary-section h3 {
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    .solutions-btn {
        background-color: #b8c832 !important;
        border: none !important;
        color: #000;
        padding: 8px 10px !important;
        font-weight: 400 !important;
    }

    .overall-stats-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
        margin-bottom: 20px;
    }

    .stat-card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
    }

    .stat-label {
        color: #666;
        font-size: 14px;
    }

    .sectional-summary-section {
        margin-bottom: 30px;
    }

    .sectional-summary-section h3 {
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
    }

    .sectional-table-container {
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .sectional-table-container table {
        margin: 0;
    }

    .sectional-table-container thead {
        background-color: #f8f9fa;
    }

    .sectional-table-container th {
        padding: 15px;
        font-weight: 600;
        color: #333;
        border: none;
    }

    .sectional-table-container th.text-center {
        text-align: center;
    }

    .sectional-table-container td {
        padding: 15px;
        border: none;
    }

    .sectional-table-container td.text-center {
        text-align: center;
    }

    .sectional-table-container td.section-name {
        font-weight: 500;
    }

    .question-distribution-section h3 {
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
    }

    .distribution-stats {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        margin-bottom: 20px;
    }

    .distribution-card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .distribution-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .distribution-value.correct {
        color: #28a745;
    }

    .distribution-value.incorrect {
        color: #dc3545;
    }

    .distribution-value.skipped {
        color: #ffc107;
    }

    .distribution-label {
        color: #666;
        font-size: 14px;
    }

    .distribution-progress {
        background: white;
        padding: 10px;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .distribution-progress .dist-progress {
        height: 10px;
        border-radius: 10px;
        background-color: #e9ecef;
        display: flex;
    }

    .distribution-progress .dist-progress-bar:first-child {
        border-radius: 10px;
    }

    .solutions-section {
        display: none;
    }

    .solutions-section.active {
        display: block;
    }

    .previous-attempts-section {
        display: none;
    }

    .previous-attempts-section.active {
        display: block;
    }

    .quiz-summary-sections.hidden {
        display: none;
    }

    .back-to-summary-btn {
        background-color: #6c757d !important;
        border: none !important;
        color: white !important;
        padding: 8px 16px !important;
        border-radius: 20px !important;
        font-weight: 500 !important;
    }

    .previous-attempts-btn {
        background-color: #17a2b8 !important;
        border: none !important;
        color: white !important;
        padding: 8px 10px !important;
        font-weight: 400 !important;
        margin-left: 10px !important;
    }
    .retry-btn{
        background-color: #28a745 !important;
        border: none !important;
        color: white !important;
        padding: 8px 10px !important;
        font-weight: 400 !important;
        margin-left: 10px !important;
        min-width: 100px;
    }
    .result-btns{
       font-size: 12px !important;
    }

    .attempts-cards-container {
        margin-bottom: 20px;
    }

    .attempt-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        margin-bottom: 15px;
        padding: 20px;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .attempt-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .attempt-card-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 15px;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 10px;
    }

    .attempt-quiz-name {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
        flex: 1;
    }

    .attempt-quiz-type {
        background-color: #17a2b8;
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
    }

    .attempt-card-body {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
    }

    .attempt-stat {
        text-align: center;
    }

    .attempt-stat-value {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
    }

    .attempt-stat-label {
        color: #666;
        font-size: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .attempt-date {
        color: #666;
        font-size: 14px;
        margin-top: 10px;
        text-align: center;
        padding-top: 10px;
        border-top: 1px solid #f0f0f0;
    }

    @media (max-width: 768px) {
        .attempt-card-body {
            grid-template-columns: 1fr;
            gap: 10px;
        }

        .attempt-card-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }
    }

    .pagination-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;
        padding: 15px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .pagination-info {
        color: #666;
        font-size: 14px;
    }

    .pagination-controls {
        display: flex;
        gap: 10px;
    }

    .pagination-btn {
        background-color: #007bff !important;
        border: none !important;
        color: white !important;
        padding: 8px 16px !important;
        border-radius: 5px !important;
        font-weight: 500 !important;
        cursor: pointer !important;
    }

    .pagination-btn:disabled {
        background-color: #6c757d !important;
        cursor: not-allowed !important;
    }

    .pagination-btn:hover:not(:disabled) {
        background-color: #0056b3 !important;
    }

    .solutions-filter-container {
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .filter-label {
        font-weight: 600;
        color: #333;
        font-size: 14px;
    }

    .custom-dropdown {
        position: relative;
        display: inline-block;
        min-width: 150px;
    }

    .dropdown-selected {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 10px 15px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 500;
        color: #333;
        transition: all 0.3s ease;
    }

    .dropdown-selected:hover {
        border-color: #007bff;
    }

    .dropdown-selected.active {
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
    }

    .dropdown-arrow {
        transition: transform 0.3s ease;
    }

    .dropdown-selected.active .dropdown-arrow {
        transform: rotate(180deg);
    }

    .dropdown-options {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 2px solid #007bff;
        border-top: none;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        display: none;
    }

    .dropdown-options.show {
        display: block;
    }

    .dropdown-option {
        padding: 12px 15px;
        cursor: pointer;
        transition: background-color 0.2s ease;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .dropdown-option:last-child {
        border-bottom: none;
    }

    .dropdown-option:hover {
        background-color: #f8f9fa;
    }

    .dropdown-option.selected {
        background-color: #e3f2fd;
        color: #007bff;
        font-weight: 600;
    }

    .filter-icon {
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }

    .filter-icon.all {
        background-color: #6c757d;
    }

    .filter-icon.correct {
        background-color: #28a745;
    }

    .filter-icon.incorrect {
        background-color: #dc3545;
    }

    .filter-icon.skipped {
        background-color: #ffc107;
    }

    .option-item {
        padding: 12px 15px;
        margin-bottom: 8px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
    }

    .option-item.correct {
        border-color: #28a745;
        background-color: #d4edda;
    }

    .option-item.user-selected {
        background-color: #fff3cd;
        border-color: #ffc107;
    }

    .option-item.user-selected.incorrect {
        background-color: #f8d7da;
        border-color: #dc3545;
    }

    .option-label {
        font-weight: bold;
        margin-right: 10px;
        display: inline-block;
        min-width: 25px;
    }
    .text-warning {
        color: #b88c09 !important;
    }
    @media (max-width: 768px) {
        .overall-stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .distribution-stats {
            grid-template-columns: 1fr;
        }
        .overall-summary-section-header{
            flex-wrap: wrap;
            justify-content: flex-start !important;
            gap: 20px;
            margin-top: 12px;
        }
        .container{
            padding: 5px !important;
        }
        .quiz-result-container{
            padding: 0;
        }
        .sectional-table-container{
            overflow-x: scroll;
        }
        .prev-sec-title{
            font-size: 20px !important;
            font-weight: 500;
        }
    }
</style>
<div class="quiz-result-container">
    <div class="container">
        <!-- Quiz Summary Sections -->
        <div class="quiz-summary-sections">
            <!-- Overall Summary Section -->
            <div class="overall-summary-section">
                <div class="overall-summary-section-header d-flex justify-content-between align-items-center mb-3">
                    <h3>Overall Summary</h3>
                    <div>
                        <button class="btn btn-warning btn-sm solutions-btn result-btns" onclick="showSolutions()">
                            Solutions
                        </button>
                        <button class="btn btn-info btn-sm previous-attempts-btn result-btns" onclick="showPreviousAttempts()">
                            Previous Attempts
                        </button>
                        <button class="btn btn-info btn-sm retry-btn result-btns" onclick="retryQuiz()">
                            Previous Attempts
                        </button>
                    </div>
                </div>
            </div>
            <div class="overall-stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="overall-score">
                        --/--
                    </div>
                    <div class="stat-label">Score</div>
                </div>

                <div class="stat-card">
                    <div class="stat-value" id="overall-attempted">
                        --/--
                    </div>
                    <div class="stat-label">Attempted</div>
                </div>

                <div class="stat-card">
                    <div class="stat-value" id="overall-accuracy">
                        --%
                    </div>
                    <div class="stat-label">Accuracy</div>
                </div>

                <div class="stat-card">
                    <div class="stat-value" id="overall-time">
                        --m --s
                    </div>
                    <div class="stat-label">Time Taken</div>
                </div>
            </div>
            <!-- Sectional Summary Section -->
            <div class="sectional-summary-section d-none">
                <h3>Sectional Summary</h3>

                <div class="sectional-table-container">
                    <table class="table table-striped mb-0">
                        <thead>
                        <tr>
                            <th>Section</th>
                            <th class="text-center">Score</th>
                            <th class="text-center">Attempted</th>
                            <th class="text-center">Accuracy</th>
                            <th class="text-center">Time</th>
                        </tr>
                        </thead>
                        <tbody id="sectional-summary-tbody">
                        <!-- Dynamic content will be inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Question Distribution Section -->
            <div class="question-distribution-section">
                <h3>Question Distribution</h3>

                <div class="distribution-stats">
                    <div class="distribution-card">
                        <div class="distribution-value correct" id="correct-count">
                            --
                        </div>
                        <div class="distribution-label">Correct</div>
                    </div>

                    <div class="distribution-card">
                        <div class="distribution-value incorrect" id="incorrect-count">
                            --
                        </div>
                        <div class="distribution-label">Incorrect</div>
                    </div>

                    <div class="distribution-card">
                        <div class="distribution-value skipped" id="skipped-count">
                            --
                        </div>
                        <div class="distribution-label">Skipped</div>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="distribution-progress">
                    <div class="dist-progress">
                        <div class="dist-progress-bar bg-success" role="progressbar" id="correct-progress"></div>
                        <div class="dist-progress-bar bg-danger" role="progressbar" id="incorrect-progress"></div>
                        <div class="dist-progress-bar bg-warning" role="progressbar" id="skipped-progress"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Solutions Section -->
        <div class="solutions-section">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3>Solutions</h3>
                <button class="btn btn-secondary btn-sm back-to-summary-btn" onclick="showSummary()">
                    Back to Summary
                </button>
            </div>

            <div class="solutions-content">
                <!-- Filter Section -->
                <div class="solutions-filter-container">
                    <span class="filter-label">Filter by:</span>
                    <div class="custom-dropdown" id="solutions-filter-dropdown">
                        <div class="dropdown-selected" onclick="toggleFilterDropdown()">
                            <span id="selected-filter-text">
                                <span class="filter-icon all"></span>
                                All Questions
                            </span>
                            <i class="fas fa-chevron-down dropdown-arrow"></i>
                        </div>
                        <div class="dropdown-options" id="filter-options">
                            <div class="dropdown-option selected" onclick="selectFilter('all')" data-filter="all">
                                <span class="filter-icon all"></span>
                                All Questions
                            </div>
                            <div class="dropdown-option" onclick="selectFilter('correct')" data-filter="correct">
                                <span class="filter-icon correct"></span>
                                Correct
                            </div>
                            <div class="dropdown-option" onclick="selectFilter('incorrect')" data-filter="incorrect">
                                <span class="filter-icon incorrect"></span>
                                Incorrect
                            </div>
                            <div class="dropdown-option" onclick="selectFilter('skipped')" data-filter="skipped">
                                <span class="filter-icon skipped"></span>
                                Skipped
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info" id="solutions-info">
                    <i class="fas fa-info-circle"></i>
                    Solutions will be displayed here. This section will show detailed explanations for each question.
                </div>

                <!-- Solutions content will be dynamically loaded here -->
                <div id="solutions-list">
                    <!-- Dynamic solutions content -->
                </div>
            </div>
        </div>

        <!-- Previous Attempts Section -->
        <div class="previous-attempts-section">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3 class="prev-sec-title">Previous Attempts</h3>
                <button class="btn btn-secondary btn-sm back-to-summary-btn" onclick="showSummary()">
                    Back to Summary
                </button>
            </div>

            <div class="previous-attempts-content">
                <div class="alert alert-info" id="attempts-info">
                    <i class="fas fa-info-circle"></i>
                    Loading previous attempts...
                </div>

                <!-- Previous attempts cards -->
                <div class="attempts-cards-container" id="attempts-cards-container" style="display: none;">
                    <div id="attempts-cards-list">
                        <!-- Dynamic content will be inserted here -->
                    </div>
                </div>

                <!-- Pagination Controls -->
                <div class="pagination-container" id="pagination-container" style="display: none;">
                    <div class="pagination-info" id="pagination-info">
                        Showing 1-20 of 0 attempts
                    </div>
                    <div class="pagination-controls">
                        <button class="btn pagination-btn" id="prev-btn" onclick="goToPreviousPage()" disabled>
                            <i class="fas fa-chevron-left"></i> Previous
                        </button>
                        <button class="btn pagination-btn" id="next-btn" onclick="goToNextPage()">
                            Next <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    let resultData;
    let skippedQuestions;
    let correctQuestions;
    let inCorrectQuestions;
    let currentPage = 1;
    let totalQuizzesAttempted = 0;
    let isLoadingAttempts = false;
    let itemsPerPage = 20;
    let currentFilter = 'all';
    let filteredSolutions = [];

    function getResultData() {
        const userResponseObj = JSON.parse(qaObj);
        const questionMap = new Map();
        questions.forEach(q => questionMap.set(q.id, q));

        const statsMap = new Map();
        quizStatisticsList.forEach(s => statsMap.set(s.objId, s));

        const userMap = new Map();
        userResponseObj.userAnswers.forEach(u => userMap.set(u.id, u));

        return questions.map(q => {
            const key = String(q.id); // use string key
            const stat = statsMap.get(key) || {};
            const user = userMap.get(key) || {};
            return { ...q, ...stat, ...user };
        });
    }

    function getSkippedQuestions(data){
        let result = new Array(data.length);
        let count = 0;

        for (let i = 0; i < data.length; i++) {
            if (data[i].userOption === "-1") {
                result[count++] = data[i];
            }
        }
        result.length = count;
        return result
    }

    function getCorrectQuestions(data){
        const result = [];
        for (let i = 0; i < data.length; i++) {
            const item = data[i];
            if (item.userOption !== "-1" && item.userOption === item.correctOption) {
                result.push(item);
            }
        }
        return result
    }

    function getInCorrectQuestions(data){
        const result = [];
        for (let i = 0; i < data.length; i++) {
            const item = data[i];
            if (item.userOption !== "-1" && item.userOption !== item.correctOption) {
                result.push(item);
            }
        }
        return result
    }

    // Calculate overall statistics
    function calculateOverallStats() {
        const totalQuestions = questions.length;
        const correctCount = correctQuestions.length;
        const incorrectCount = inCorrectQuestions.length;
        const skippedCount = skippedQuestions.length;
        const attemptedCount = correctCount + incorrectCount;
        const accuracy = attemptedCount > 0 ? Math.round((correctCount / attemptedCount) * 100) : 0;

        // Calculate total time (if available)
        let totalTime = 0;
        if (typeof userTotalTime !== 'undefined' && userTotalTime) {
            totalTime = userTotalTime;
        } else {
            // Fallback: sum up individual question times
            resultData.forEach(q => {
                if (q.userTime) {
                    totalTime += parseFloat(q.userTime);
                }
            });
        }

        const minutes = Math.floor(totalTime / 60);
        const seconds = Math.floor(totalTime % 60);

        return {
            score: correctCount + "/" + totalQuestions,
            attempted: attemptedCount + "/" + totalQuestions,
            accuracy: accuracy + "%",
            time: minutes + "m " + seconds + "s",
            correctCount,
            incorrectCount,
            skippedCount,
            totalQuestions
        };
    }

    // Calculate sectional statistics
    function calculateSectionalStats() {
        const sectionStats = {};

        resultData.forEach(q => {
            const section = q.subject || 'General';
            if (!sectionStats[section]) {
                sectionStats[section] = {
                    total: 0,
                    correct: 0,
                    incorrect: 0,
                    skipped: 0,
                    totalTime: 0
                };
            }

            sectionStats[section].total++;

            if (q.userOption === "-1") {
                sectionStats[section].skipped++;
            } else if (q.userOption === q.correctOption) {
                sectionStats[section].correct++;
            } else {
                sectionStats[section].incorrect++;
            }

            if (q.userTime) {
                sectionStats[section].totalTime += parseFloat(q.userTime);
            }
        });

        // Convert to array format for table display
        const sectionalArray = [];
        Object.keys(sectionStats).forEach(section => {
            const stats = sectionStats[section];
            const attempted = stats.correct + stats.incorrect;
            const accuracy = attempted > 0 ? Math.round((stats.correct / attempted) * 100) : 0;
            const minutes = Math.floor(stats.totalTime / 60);
            const seconds = Math.floor(stats.totalTime % 60);

            sectionalArray.push({
                section: section,
                score: stats.correct,
                attempted: attempted,
                accuracy: accuracy + "%",
                time: minutes + "m"
            });
        });

        return sectionalArray;
    }

    // Populate the UI with calculated data
    function populateQuizResultUI() {
        resultData = getResultData();
        skippedQuestions = getSkippedQuestions(resultData);
        correctQuestions = getCorrectQuestions(resultData);
        inCorrectQuestions = getInCorrectQuestions(resultData);

        const overallStats = calculateOverallStats();
        const sectionalStats = calculateSectionalStats();

        // Update overall summary
        document.getElementById('overall-score').textContent = overallStats.score;
        document.getElementById('overall-attempted').textContent = overallStats.attempted;
        document.getElementById('overall-accuracy').textContent = overallStats.accuracy;
        document.getElementById('overall-time').textContent = overallStats.time;

        // Update question distribution
        document.getElementById('correct-count').textContent = overallStats.correctCount;
        document.getElementById('incorrect-count').textContent = overallStats.incorrectCount;
        document.getElementById('skipped-count').textContent = overallStats.skippedCount;

        // Update progress bar
        const total = overallStats.totalQuestions;
        const correctPercent = (overallStats.correctCount / total) * 100;
        const incorrectPercent = (overallStats.incorrectCount / total) * 100;
        const skippedPercent = (overallStats.skippedCount / total) * 100;

        document.getElementById('correct-progress').style.width = correctPercent + "%";
        document.getElementById('incorrect-progress').style.width = incorrectPercent + "%";
        document.getElementById('skipped-progress').style.width = skippedPercent + "%";

        // Update sectional summary table
        const tbody = document.getElementById('sectional-summary-tbody');
        tbody.innerHTML = '';

        sectionalStats.forEach(section => {
            const row = document.createElement('tr');
            row.innerHTML =
                "<td class='section-name'>" + section.section + "</td>" +
                "<td class='text-center'>" + section.score + "</td>" +
                "<td class='text-center'>" + section.attempted + "</td>" +
                "<td class='text-center'>" + section.accuracy + "</td>" +
                "<td class='text-center'>" + section.time + "</td>";
            tbody.appendChild(row);
        });

        if(sectionDtl && sectionDtl!=null && sectionDtl!=undefined){
            document.querySelector('.sectional-summary-section').classList.remove('d-none');
        }
    }



    // Toggle between summary, solutions, and previous attempts views
    function showSolutions() {
        document.querySelector('.quiz-summary-sections').classList.add('hidden');
        document.querySelector('.solutions-section').classList.add('active');
        document.querySelector('.previous-attempts-section').classList.remove('active');
        loadSolutions();
        addMathjax()
    }

    function showPreviousAttempts() {
        document.querySelector('.quiz-summary-sections').classList.add('hidden');
        document.querySelector('.solutions-section').classList.remove('active');
        document.querySelector('.previous-attempts-section').classList.add('active');

        // Reset pagination if this is a fresh load
        currentPage = 1;
        loadPreviousAttempts();
    }

    function showSummary() {
        document.querySelector('.quiz-summary-sections').classList.remove('hidden');
        document.querySelector('.solutions-section').classList.remove('active');
        document.querySelector('.previous-attempts-section').classList.remove('active');
    }

    function retryQuiz(){
        window.location.reload();
    }

    // Load solutions content
    function loadSolutions() {
        // Reset filter to 'all' when loading solutions
        currentFilter = 'all';
        resetFilterDropdown();

        // Apply current filter and display
        applyFilter();
    }

    // Apply filter to solutions
    function applyFilter() {
        const solutionsList = document.getElementById('solutions-list');
        const solutionsInfo = document.getElementById('solutions-info');
        solutionsList.innerHTML = '';

        if (!resultData || resultData.length === 0) {
            solutionsInfo.style.display = 'block';
            solutionsInfo.innerHTML = '<i class="fas fa-info-circle"></i> No solutions available.';
            return;
        }

        // Get filtered data based on current filter
        let dataToShow = [];
        switch (currentFilter) {
            case 'all':
                dataToShow = resultData;
                break;
            case 'correct':
                dataToShow = correctQuestions;
                break;
            case 'incorrect':
                dataToShow = inCorrectQuestions;
                break;
            case 'skipped':
                dataToShow = skippedQuestions;
                break;
        }

        if (dataToShow && dataToShow.length > 0) {
            solutionsInfo.style.display = 'none';
            dataToShow.forEach((question, index) => {
                // Find the original question number from resultData
                const originalIndex = resultData.findIndex(q => q.id === question.id);
                const questionNumber = originalIndex + 1;
                const solutionCard = createSolutionCard(question, questionNumber);
                solutionsList.appendChild(solutionCard);
            });
        } else {
            solutionsInfo.style.display = 'block';
            const filterText = currentFilter.charAt(0).toUpperCase() + currentFilter.slice(1);
            solutionsInfo.innerHTML = '<i class="fas fa-info-circle"></i> No ' + filterText.toLowerCase() + ' questions found.';
        }
        addMathjax()
    }

    // Create individual solution card
    function createSolutionCard(question, questionNumber) {
        const card = document.createElement('div');
        card.className = 'card mb-3';

        const userAnswer = question.userOption;
        const correctAnswer = question.correctOption;
        const isCorrect = userAnswer === correctAnswer;
        const isSkipped = userAnswer === "-1";

        let statusClass = 'text-warning';
        let statusText = 'Skipped';
        let statusIcon = 'fas fa-minus-circle';

        if (!isSkipped) {
            if (isCorrect) {
                statusClass = 'text-success';
                statusText = 'Correct';
                statusIcon = 'fas fa-check-circle';
            } else {
                statusClass = 'text-danger';
                statusText = 'Incorrect';
                statusIcon = 'fas fa-times-circle';
            }
        }

        // Create options HTML
        const optionsHtml = createOptionsHtml(question, userAnswer, correctAnswer);

        card.innerHTML =
            "<div class='card-header d-flex justify-content-between align-items-center'>" +
                "<h6 class='mb-0'>Question " + questionNumber + "</h6>" +
                "<span class='" + statusClass + "'>" +
                    "<i class='" + statusIcon + "'></i> " + statusText +
                "</span>" +
            "</div>" +
            "<div class='card-body'>" +
                "<div class='question-text mb-4'>" +
                    "<strong>Question:</strong><br>" +
                    "<div class='mt-2'>" + (question.ps || 'Question text not available') + "</div>" +
                "</div>" +

                "<div class='options-container mb-3'>" +
                    "<strong>Options:</strong>" +
                    "<div class='mt-2'>" + optionsHtml + "</div>" +
                "</div>" +

                (question.answerDescription ?
                "<div class='explanation'>" +
                    "<strong>Explanation:</strong>" +
                    "<div class='mt-2 p-3 bg-light rounded'>" +
                        question.answerDescription +
                    "</div>" +
                "</div>" : '') +
            "</div>";

        return card;
    }

    // Create options HTML with proper styling
    function createOptionsHtml(question, userAnswer, correctAnswer) {
        const options = [
            { label: 'A', text: question.op1 },
            { label: 'B', text: question.op2 },
            { label: 'C', text: question.op3 },
            { label: 'D', text: question.op4 }
        ];

        // Add option E if it exists
        if (question.op5) {
            options.push({ label: 'E', text: question.op5 });
        }

        let optionsHtml = '';

        options.forEach((option, index) => {
            if (option.text) {
                let optionClass = 'option-item';

                // Add correct styling
                if (parseInt(correctAnswer) === (index+1)) {
                    optionClass += ' correct';
                }

                // Add user selection styling
                if (option.label === userAnswer && userAnswer !== "-1") {
                    optionClass += ' user-selected';
                    if (userAnswer !== correctAnswer) {
                        optionClass += ' incorrect';
                    }
                }

                optionsHtml +=
                    "<div class='" + optionClass + "'>" +
                        "<span class='option-label'>" + option.label + ".</span>" +
                        option.text +
                    "</div>";
            }
        });

        return optionsHtml;
    }

    // Get answer text based on option
    function getUserAnswerText(question, option) {
        const optionMap = {
            'A': question.op1,
            'B': question.op2,
            'C': question.op3,
            'D': question.op4
        };
        if(question.op5){
            optionMap['E'] = question.op5;
        }
        return optionMap[option] || option || 'Not available';
    }

    // Load previous attempts
    function loadPreviousAttempts() {
        if (isLoadingAttempts) return;

        isLoadingAttempts = true;
        document.getElementById('attempts-info').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading previous attempts...';
        document.getElementById('attempts-cards-container').style.display = 'none';
        document.getElementById('pagination-container').style.display = 'none';

        // Disable pagination buttons
        document.getElementById('prev-btn').disabled = true;
        document.getElementById('next-btn').disabled = true;

        const startIndex = ((currentPage - 1) * itemsPerPage) + 1;
        const url = '/analytics/getUsersHistoryForAllQuizzes?siteId=1&startIndex=' + startIndex;

        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.quizHistory && data.quizHistory.length > 0) {
                    // Clear previous results and display new ones
                    document.getElementById('attempts-cards-list').innerHTML = '';
                    displayPreviousAttempts(data.quizHistory);
                    totalQuizzesAttempted = parseInt(data.totalQuizzesAttempted) || 0;

                    // Show cards and pagination
                    document.getElementById('attempts-cards-container').style.display = 'block';
                    document.getElementById('attempts-info').style.display = 'none';
                    document.getElementById('pagination-container').style.display = 'flex';

                    // Update pagination info and controls
                    updatePaginationControls();
                } else {
                    if (currentPage === 1) {
                        document.getElementById('attempts-info').innerHTML = '<i class="fas fa-info-circle"></i> No previous attempts found.';
                    } else {
                        // If no data on current page, go back to previous page
                        currentPage = Math.max(1, currentPage - 1);
                        loadPreviousAttempts();
                        return;
                    }
                }
            })
            .catch(error => {
                console.error('Error loading previous attempts:', error);
                document.getElementById('attempts-info').innerHTML = '<i class="fas fa-exclamation-triangle"></i> Error loading previous attempts. Please try again.';
            })
            .finally(() => {
                isLoadingAttempts = false;
            });
    }

    // Display previous attempts in cards
    function displayPreviousAttempts(attempts) {
        const cardsList = document.getElementById('attempts-cards-list');

        attempts.forEach(attempt => {
            const card = document.createElement('div');
            card.className = 'attempt-card';

            // Format date
            const date = new Date(attempt.dateCreated);
            const formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

            // Handle quiz type
            let quizType = attempt.quizType || '';
            if (quizType === '') {
                quizType = 'Play';
            }

            card.innerHTML =
                "<div class='attempt-card-header'>" +
                    "<h5 class='attempt-quiz-name'>" + (attempt.quizName || 'N/A') + "</h5>" +
                    "<span class='attempt-quiz-type'>" + quizType + "</span>" +
                "</div>" +
                "<div class='attempt-card-body'>" +
                    "<div class='attempt-stat'>" +
                        "<div class='attempt-stat-value'>" + (attempt.noOfQuestions || 0) + "</div>" +
                        "<div class='attempt-stat-label'>Questions</div>" +
                    "</div>" +
                    "<div class='attempt-stat'>" +
                        "<div class='attempt-stat-value'>" + (attempt.points || 0) + "</div>" +
                        "<div class='attempt-stat-label'>Points</div>" +
                    "</div>" +
                    "<div class='attempt-stat'>" +
                        "<div class='attempt-stat-value'>" + (attempt.userTime || 0) + "s</div>" +
                        "<div class='attempt-stat-label'>Time</div>" +
                    "</div>" +
                "</div>" +
                "<div class='attempt-date'>" +
                    "<i class='fas fa-calendar-alt'></i> " + formattedDate +
                "</div>";

            cardsList.appendChild(card);
        });
    }

    // Update pagination controls
    function updatePaginationControls() {
        const totalPages = Math.ceil(totalQuizzesAttempted / itemsPerPage);
        const startItem = ((currentPage - 1) * itemsPerPage) + 1;
        const endItem = Math.min(currentPage * itemsPerPage, totalQuizzesAttempted);

        // Update pagination info
        document.getElementById('pagination-info').textContent =
            'Showing ' + startItem + '-' + endItem + ' of ' + totalQuizzesAttempted + ' attempts';

        // Update button states
        document.getElementById('prev-btn').disabled = currentPage <= 1;
        document.getElementById('next-btn').disabled = currentPage >= totalPages;
    }

    // Go to previous page
    function goToPreviousPage() {
        if (currentPage > 1) {
            currentPage--;
            loadPreviousAttempts();
        }
    }

    // Go to next page
    function goToNextPage() {
        const totalPages = Math.ceil(totalQuizzesAttempted / itemsPerPage);
        if (currentPage < totalPages) {
            currentPage++;
            loadPreviousAttempts();
        }
    }

    // Filter dropdown functions
    function toggleFilterDropdown() {
        const dropdown = document.getElementById('filter-options');
        const selected = document.querySelector('.dropdown-selected');

        dropdown.classList.toggle('show');
        selected.classList.toggle('active');
    }

    function selectFilter(filterType) {
        currentFilter = filterType;

        // Update selected option styling
        document.querySelectorAll('.dropdown-option').forEach(option => {
            option.classList.remove('selected');
        });
        document.querySelector('[data-filter="' + filterType + '"]').classList.add('selected');

        // Update selected text and icon
        const selectedText = document.getElementById('selected-filter-text');
        const filterTexts = {
            'all': '<span class="filter-icon all"></span> All Questions',
            'correct': '<span class="filter-icon correct"></span> Correct',
            'incorrect': '<span class="filter-icon incorrect"></span> Incorrect',
            'skipped': '<span class="filter-icon skipped"></span> Skipped'
        };
        selectedText.innerHTML = filterTexts[filterType];

        // Close dropdown
        document.getElementById('filter-options').classList.remove('show');
        document.querySelector('.dropdown-selected').classList.remove('active');

        // Apply filter
        applyFilter();
    }

    function resetFilterDropdown() {
        currentFilter = 'all';
        document.querySelectorAll('.dropdown-option').forEach(option => {
            option.classList.remove('selected');
        });
        document.querySelector('[data-filter="all"]').classList.add('selected');
        document.getElementById('selected-filter-text').innerHTML = '<span class="filter-icon all"></span> All Questions';
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        const dropdown = document.getElementById('solutions-filter-dropdown');
        if (dropdown && !dropdown.contains(event.target)) {
            document.getElementById('filter-options').classList.remove('show');
            document.querySelector('.dropdown-selected').classList.remove('active');
        }
    });

</script>